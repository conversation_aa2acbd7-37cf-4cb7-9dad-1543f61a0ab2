'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
// import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ProgressTracker from '@/components/progress/ProgressTracker';

export default function ProgressPage() {
  const { data: session, status } = useSession();
  // const router = useRouter();

  if (status === 'loading') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Sign In Required
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You need to sign in to view your learning progress.
          </p>
          <div className="flex gap-4 justify-center">
            <Button asChild>
              <Link href="/login">Sign In</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/">Go Home</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/dashboard" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Learning Progress
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Track your learning journey and achievements
            </p>
          </div>
          
          <div className="flex gap-3">
            <Button asChild variant="outline">
              <Link href="/resources">Browse Resources</Link>
            </Button>
            <Button asChild>
              <Link href="/assessment">Take Assessment</Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Progress Tracker Component */}
      <ProgressTracker userId={session?.user?.id} />

      {/* Quick Actions */}
      <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button asChild variant="outline" className="h-auto p-4">
            <Link href="/resources" className="flex flex-col items-center gap-2">
              <span className="text-lg">📚</span>
              <span className="font-medium">Find New Resources</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Discover learning materials
              </span>
            </Link>
          </Button>
          
          <Button asChild variant="outline" className="h-auto p-4">
            <Link href="/career-paths" className="flex flex-col items-center gap-2">
              <span className="text-lg">🎯</span>
              <span className="font-medium">Explore Career Paths</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Plan your transition
              </span>
            </Link>
          </Button>
          
          <Button asChild variant="outline" className="h-auto p-4">
            <Link href="/forum" className="flex flex-col items-center gap-2">
              <span className="text-lg">💬</span>
              <span className="font-medium">Join Community</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Connect with others
              </span>
            </Link>
          </Button>
        </div>
      </div>

      {/* Tips for Better Progress */}
      <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
          💡 Tips for Better Progress
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <p className="text-blue-800 dark:text-blue-200">
              <strong>Set Daily Goals:</strong> Aim to complete at least one learning activity per day to build momentum.
            </p>
            <p className="text-blue-800 dark:text-blue-200">
              <strong>Rate Resources:</strong> Help others by rating and reviewing resources you&apos;ve completed.
            </p>
          </div>
          <div className="space-y-2">
            <p className="text-blue-800 dark:text-blue-200">
              <strong>Track Progress:</strong> Bookmark interesting resources and mark your progress as you learn.
            </p>
            <p className="text-blue-800 dark:text-blue-200">
              <strong>Stay Consistent:</strong> Regular learning, even in small amounts, leads to better retention.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
