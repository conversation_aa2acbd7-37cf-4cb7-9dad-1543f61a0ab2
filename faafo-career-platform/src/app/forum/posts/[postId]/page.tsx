'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, User, Calendar, MessageSquare, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';

interface ForumPost {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  author: {
    id: string;
    email: string;
    name?: string;
  };
  replies: Array<{
    id: string;
    content: string;
    createdAt: string;
    author: {
      id: string;
      email: string;
      name?: string;
    };
  }>;
}

interface ReplyForm {
  content: string;
}

export default function ForumPostPage({ params }: { params: Promise<{ postId: string }> }) {
  const { status } = useSession();
  const router = useRouter();
  const [post, setPost] = useState<ForumPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmittingReply, setIsSubmittingReply] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<ReplyForm>();

  const contentValue = watch('content', '');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchPost();
    }
  }, [status, fetchPost, router]);

  const fetchPost = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/forum/posts');

      if (!response.ok) {
        throw new Error('Failed to fetch posts');
      }

      const posts = await response.json();
      const resolvedParams = await params;
      const foundPost = posts.find((p: ForumPost) => p.id === resolvedParams.postId);

      if (!foundPost) {
        setError('Post not found');
        return;
      }

      setPost(foundPost);
    } catch (err) {
      console.error('Error fetching post:', err);
      setError('Failed to load post');
    } finally {
      setLoading(false);
    }
  }, [params.postId]);

  const onSubmitReply = async (data: ReplyForm) => {
    if (status !== 'authenticated') {
      setError('You must be logged in to reply');
      return;
    }

    setIsSubmittingReply(true);
    setError(null);

    try {
      const response = await fetch(`/api/forum/posts/${params.postId}/replies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: data.content.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create reply');
      }

      const newReply = await response.json();
      
      // Add the new reply to the post
      if (post) {
        setPost({
          ...post,
          replies: [...post.replies, newReply],
        });
      }
      
      reset(); // Clear the form
    } catch (err) {
      console.error('Error creating reply:', err);
      setError(err instanceof Error ? err.message : 'Failed to create reply');
    } finally {
      setIsSubmittingReply(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDisplayName = (author: { name?: string; email: string }) => {
    return author.name || author.email.split('@')[0];
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading post...</div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to view this post.</p>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">{error || 'Post not found'}</p>
          <Link href="/forum" className="text-blue-600 hover:underline mt-4 inline-block">
            Back to Forum
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-6">
        <Link
          href="/forum"
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Forum
        </Link>
      </div>

      {/* Main Post */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          {post.title}
        </h1>
        
        <div className="flex items-center gap-4 mb-6 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <User className="h-4 w-4" />
            {getDisplayName(post.author)}
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            {formatDate(post.createdAt)}
          </div>
          <div className="flex items-center gap-1">
            <MessageSquare className="h-4 w-4" />
            {post.replies.length} {post.replies.length === 1 ? 'reply' : 'replies'}
          </div>
        </div>

        <div className="prose dark:prose-invert max-w-none">
          <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
            {post.content}
          </p>
        </div>
      </div>

      {/* Replies Section */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Replies ({post.replies.length})
        </h2>

        {post.replies.map((reply) => (
          <div
            key={reply.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ml-8"
          >
            <div className="flex items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                {getDisplayName(reply.author)}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {formatDate(reply.createdAt)}
              </div>
            </div>
            <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
              {reply.content}
            </p>
          </div>
        ))}

        {/* Reply Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Add a Reply
          </h3>

          {error && (
            <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmitReply)} className="space-y-4">
            <div>
              <textarea
                id="content"
                rows={4}
                {...register('content', {
                  required: 'Reply content is required',
                  maxLength: {
                    value: 2000,
                    message: 'Reply must be 2000 characters or less',
                  },
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Write your reply..."
                disabled={isSubmittingReply}
              />
              <div className="flex justify-between mt-1">
                {errors.content && (
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.content.message}</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 ml-auto">
                  {contentValue.length}/2000 characters
                </p>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isSubmittingReply}
              className="flex items-center gap-2"
            >
              {isSubmittingReply ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Posting...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  Post Reply
                </>
              )}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
