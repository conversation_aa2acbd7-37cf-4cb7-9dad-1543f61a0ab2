'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { ArrowLeft, Star, ExternalLink, BookOpen, Video, Headphones, Shield, Code, Clock, User, Lightbulb, Target, TrendingUp, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Recommendation {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  skillLevel: string;
  author?: string;
  duration?: string;
  cost: string;
  averageRating: number;
  totalRatings: number;
  careerPaths: {
    id: string;
    name: string;
    slug: string;
  }[];
  recommendationScore: number;
  recommendationReasons: string[];
}

interface RecommendationData {
  recommendations: Recommendation[];
  totalCount: number;
  hasUserData: boolean;
  basedOnAssessment: boolean;
}

const getResourceIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'article':
      return BookOpen;
    case 'video':
      return Video;
    case 'podcast':
      return Headphones;
    case 'course':
      return BookOpen;
    case 'certification':
      return Shield;
    case 'tutorial':
      return Code;
    default:
      return BookOpen;
  }
};

const getCostBadgeColor = (cost: string) => {
  switch (cost.toLowerCase()) {
    case 'free':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'freemium':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'paid':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

const getSkillLevelColor = (level: string) => {
  switch (level.toLowerCase()) {
    case 'beginner':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'advanced':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

export default function RecommendationsPage() {
  const { data: session, status } = useSession();
  const [recommendations, setRecommendations] = useState<RecommendationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    category: 'all',
    skillLevel: 'all',
    limit: 10,
  });

  useEffect(() => {
    fetchRecommendations();
  }, [filters]);

  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (filters.category !== 'all') params.append('category', filters.category);
      if (filters.skillLevel !== 'all') params.append('skillLevel', filters.skillLevel);
      params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/recommendations?${params.toString()}`);
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setRecommendations(result.data);
        } else {
          throw new Error(result.error || 'Failed to fetch recommendations');
        }
      } else {
        throw new Error('Failed to fetch recommendations');
      }
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      setError(error instanceof Error ? error.message : 'Failed to load recommendations');
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading recommendations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/resources" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Resources
          </Link>
        </Button>
        
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Personalized Recommendations
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            {recommendations?.hasUserData 
              ? 'Resources tailored to your interests, experience level, and learning goals.'
              : 'Popular and highly-rated resources to get you started on your learning journey.'
            }
          </p>
        </div>
      </div>

      {/* Status Banner */}
      {status === 'unauthenticated' && (
        <div className="mb-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <div className="flex items-center gap-3">
            <Lightbulb className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <div>
              <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                Get Better Recommendations
              </h3>
              <p className="text-blue-800 dark:text-blue-200 text-sm">
                Sign in and take our assessment to receive personalized learning recommendations based on your goals and experience.
              </p>
            </div>
          </div>
          <div className="mt-4 flex gap-3">
            <Button asChild size="sm">
              <Link href="/login">Sign In</Link>
            </Button>
            <Button asChild variant="outline" size="sm">
              <Link href="/assessment">Take Assessment</Link>
            </Button>
          </div>
        </div>
      )}

      {recommendations && !recommendations.basedOnAssessment && status === 'authenticated' && (
        <div className="mb-8 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-6">
          <div className="flex items-center gap-3">
            <Target className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            <div>
              <h3 className="font-semibold text-orange-900 dark:text-orange-100">
                Complete Your Assessment
              </h3>
              <p className="text-orange-800 dark:text-orange-200 text-sm">
                Take our career assessment to get recommendations tailored to your specific goals and experience level.
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Button asChild size="sm">
              <Link href="/assessment">Take Assessment</Link>
            </Button>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Refine Recommendations
        </h3>
        <div className="flex flex-wrap gap-4">
          <select
            value={filters.category}
            onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
          >
            <option value="all">All Categories</option>
            <option value="cybersecurity">Cybersecurity</option>
            <option value="data-science">Data Science</option>
            <option value="artificial-intelligence">AI & Machine Learning</option>
            <option value="web-development">Web Development</option>
            <option value="digital-marketing">Digital Marketing</option>
            <option value="blockchain">Blockchain</option>
            <option value="project-management">Project Management</option>
          </select>

          <select
            value={filters.skillLevel}
            onChange={(e) => setFilters(prev => ({ ...prev, skillLevel: e.target.value }))}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>

          <select
            value={filters.limit}
            onChange={(e) => setFilters(prev => ({ ...prev, limit: parseInt(e.target.value) }))}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
          >
            <option value={5}>5 recommendations</option>
            <option value={10}>10 recommendations</option>
            <option value={20}>20 recommendations</option>
          </select>
        </div>
      </div>

      {/* Recommendations */}
      {error ? (
        <div className="text-center py-8">
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <Button onClick={fetchRecommendations} variant="outline">
            Try Again
          </Button>
        </div>
      ) : recommendations && recommendations.recommendations.length > 0 ? (
        <div className="space-y-6">
          {recommendations.recommendations.map((resource, index) => {
            const ResourceIcon = getResourceIcon(resource.type);
            
            return (
              <div key={resource.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
                <div className="flex items-start gap-4">
                  {/* Recommendation Rank */}
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                        #{index + 1}
                      </span>
                    </div>
                  </div>

                  {/* Resource Icon */}
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                      <ResourceIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                          {resource.title}
                        </h3>
                        {resource.author && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            by {resource.author}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                            {resource.recommendationScore.toFixed(1)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <p className="text-gray-700 dark:text-gray-300 mb-4 text-sm">
                      {resource.description}
                    </p>

                    {/* Metadata */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getCostBadgeColor(resource.cost)}`}>
                        {resource.cost.charAt(0).toUpperCase() + resource.cost.slice(1)}
                      </span>
                      
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getSkillLevelColor(resource.skillLevel)}`}>
                        {resource.skillLevel.charAt(0).toUpperCase() + resource.skillLevel.slice(1)}
                      </span>
                      
                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded text-xs font-medium">
                        {resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}
                      </span>
                      
                      {resource.duration && (
                        <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs font-medium flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {resource.duration}
                        </span>
                      )}
                    </div>

                    {/* Rating */}
                    <div className="flex items-center gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-3 w-3 ${
                                star <= resource.averageRating
                                  ? 'text-yellow-500 fill-current'
                                  : 'text-gray-300 dark:text-gray-600'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-xs text-gray-600 dark:text-gray-400">
                          {resource.averageRating.toFixed(1)} ({resource.totalRatings} reviews)
                        </span>
                      </div>
                    </div>

                    {/* Recommendation Reasons */}
                    {resource.recommendationReasons.length > 0 && (
                      <div className="mb-4">
                        <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                          Why this is recommended:
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {resource.recommendationReasons.map((reason, idx) => (
                            <span
                              key={idx}
                              className="px-2 py-1 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"
                            >
                              {reason}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button asChild variant="outline" size="sm">
                        <Link href={`/resources/${resource.id}`} className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          View Details
                        </Link>
                      </Button>
                      
                      <Button asChild size="sm">
                        <a
                          href={resource.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-2"
                        >
                          Start Learning
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            No recommendations found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Try adjusting your filters or browse all resources.
          </p>
          <Button asChild>
            <Link href="/resources">Browse All Resources</Link>
          </Button>
        </div>
      )}

      {/* Call to Action */}
      <div className="mt-12 text-center">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Want More Personalized Recommendations?
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Complete your profile and take our assessment for better recommendations
        </p>
        <div className="flex gap-4 justify-center">
          <Button asChild>
            <Link href="/assessment">Take Assessment</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/profile">Update Profile</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
